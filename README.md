# Mail Auto: Multi-Tenant Email Automation System

> **Status:** Production-ready with Azure Key Vault integration and enterprise security features.  
> **Features:** Multi-format file processing, AI-powered document classification, secure credential management, automated OneDrive organization, and customer self-service onboarding.
>
> **Latest Updates:**
> * ✅ **Live Dashboard Interface**: Real-time web portal with development/production mode switching
> * ✅ **Admin Panel**: Comprehensive tenant management and system monitoring
> * ✅ **Multi-Mailbox Support**: Monitor multiple mailboxes per tenant with intelligent routing
> * ✅ **Comprehensive Tracking & Analytics**: Real-time processing statistics and failure analysis
> * ✅ **Environment-Based Filtering**: Proper separation of development vs production tenant data
> * ✅ **Web-Ready Analytics**: Dashboard data for business intelligence and troubleshooting
> * ✅ Azure Key Vault integration for secure credential storage
> * ✅ Multi-tenant customer onboarding with OAuth consent flow
> * ✅ Environment-based configuration (development/production)
> * ✅ Managed Identity support for production deployment
> * ✅ Comprehensive migration tools and documentation

---

## 🎯 What It Does

Mail Auto is an enterprise-grade email automation system that processes incoming emails with attachments, automatically classifies documents using AI, and organizes them in OneDrive with intelligent folder structures.

### **Core Pipeline (Per Tenant)**

1. **🔐 Secure Authentication**: Connects to Microsoft 365 via Graph API using Azure Key Vault stored credentials
2. **📧 Multi-Mailbox Processing**: Scans multiple configured mailboxes for unread emails with attachments
3. **📄 Multi-Format Analysis**: Extracts text from various file types using specialized processors
4. **🤖 AI Classification**: Uses OpenAI GPT to analyze content and classify document types
5. **📁 Smart Organization**: Routes documents to appropriate OneDrive folders based on configurable rules
6. **📬 Intelligent Notifications**: Sends notifications from the same mailbox that received the document
7. **📊 Comprehensive Tracking**: Logs all operations with detailed analytics and failure tracking
8. **✅ Completion**: Marks emails as read and provides detailed processing reports

### **Supported File Types**
- **PDFs**: Text extraction + OCR fallback for scanned documents
- **Microsoft Office**: Word (.docx), Excel (.xlsx), PowerPoint (.pptx)  
- **Images**: JPG, PNG, GIF, BMP, TIFF with OCR text extraction
- **Plain Text**: TXT, CSV files with direct content reading

### **AI-Powered Features**
- **Document Classification**: Automatically identifies invoices, certificates, contracts, etc.
- **Content Summarization**: Generates human-readable summaries
- **Data Extraction**: Pulls key information (supplier, amount, dates, etc.)
- **Context Awareness**: Analyzes email body text alongside attachments for better accuracy

### **Multi-Mailbox Architecture**
- **Flexible Configuration**: Monitor any number of mailboxes per tenant
- **Intelligent Routing**: Documents processed from the correct mailbox context
- **Unified Processing**: All document types can be processed from any configured mailbox
- **Smart Notifications**: Replies sent from the same mailbox that received the original email

### **Comprehensive Tracking & Analytics**
- **Real-Time Monitoring**: Track processing success/failure rates and performance metrics
- **Business Intelligence**: Daily, monthly, and yearly statistics with trend analysis
- **Failure Analysis**: Detailed error tracking for troubleshooting and system optimization
- **Web-Ready Data**: JSON exports for dashboard integration and business reporting
- **Multi-Tenant Analytics**: Isolated statistics per customer with configurable retention policies

### **Web Portal Dashboard**
- **Live Data Interface**: Real-time dashboard with 30-second auto-refresh for monitoring
- **Development/Production Modes**: Proper tenant filtering for testing vs customer environments
- **Admin Panel**: Comprehensive tenant management, system health monitoring, and error tracking
- **Interactive Charts**: Document processing trends, success rates, and system performance metrics
- **Multi-Tenant Support**: Isolated views for development accounts vs production customers
- **Responsive Design**: Modern React interface with dark/light mode support

---

## 🏗️ System Architecture

### **Project Structure**
```
Mail_Auto/
├── core/                           # Core system modules
│   ├── config.py                   # Environment-based configuration management
│   ├── key_vault_service.py        # Azure Key Vault integration
│   ├── tenant_loader.py            # Tenant discovery and credential loading
│   ├── tenant_onboarding.py        # Customer self-service onboarding
│   ├── mail_reader.py              # Microsoft Graph email processing (multi-mailbox)
│   ├── mailbox_manager.py          # Multi-mailbox configuration management
│   ├── unified_file_analyzer.py    # Main file analysis orchestrator with tracking
│   ├── router.py                   # Document routing and folder resolution
│   ├── upload_onedrive.py          # OneDrive upload operations
│   ├── notification.py             # Email notification system (mailbox-aware)
│   ├── file_processors/            # Multi-format file processors
│   │   ├── factory.py              # Processor factory pattern
│   │   ├── base.py                 # Base processor interface
│   │   ├── pdf_processor.py        # PDF text extraction + OCR
│   │   ├── docx_processor.py       # Word document processor
│   │   ├── xlsx_processor.py       # Excel spreadsheet processor
│   │   ├── pptx_processor.py       # PowerPoint processor
│   │   ├── image_processor.py      # Image OCR processor
│   │   └── text_processor.py       # Plain text processor
│   ├── interpreter/                # AI analysis module
│   │   └── chatgpt_api.py          # OpenAI GPT integration
│   ├── tracking/                   # Comprehensive tracking & analytics system
│   │   ├── models.py               # Database models and operations
│   │   ├── service.py              # Tracking service with context manager
│   │   ├── analytics.py            # Business intelligence and reporting
│   │   ├── config.py               # Tracking configuration management
│   │   └── logger.py               # Enhanced logging with structured output
│   └── web_portal/                 # React-based web dashboard
│       ├── src/                    # React TypeScript source code
│       │   ├── pages/              # Dashboard, Admin Panel, Settings
│       │   ├── components/         # Reusable UI components
│       │   ├── contexts/           # React context providers
│       │   └── services/           # API service layer
│       ├── package.json            # Node.js dependencies
│       └── vite.config.ts          # Vite build configuration
├── tenants/                        # Tenant-specific configurations
│   └── prototype/                  # Development tenant example
│       └── config.json             # Document processing rules + tracking config
├── Test/                           # Test files and utilities
│   ├── simple_tracking_test.py     # Basic tracking system test
│   └── test_tracking_system.py     # Comprehensive tracking tests
├── main.py                         # Main pipeline orchestrator with tracking
├── web_server.py                   # Flask API server for web dashboard
├── setup_development.py            # Development environment setup
├── migrate_to_keyvault.py          # Migration utility for Key Vault
├── test_multi_format.py            # Multi-format testing script
├── requirements.txt                # Python dependencies
├── .env                           # Environment configuration (create from template)
├── AZURE_SETUP.md                 # Azure AD and production setup guide
└── KEYVAULT_SETUP.md              # Key Vault configuration guide
```

---

## 🚀 Quick Start

### **Prerequisites**
- Python 3.10+ 
- Azure subscription with Key Vault access
- Microsoft 365 tenant with admin permissions
- OpenAI API key
- Tesseract OCR (for image processing)

### **1. Environment Setup**
```bash
# Clone and navigate to project
git clone <repository-url>
cd Mail_Auto

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your Azure and OpenAI credentials
```

### **2. Azure Configuration**
```bash
# Run automated setup (creates Key Vault, validates dependencies)
python setup_development.py

# Follow the Azure setup guide for detailed configuration
# See: AZURE_SETUP.md and KEYVAULT_SETUP.md
```

### **3. Run the System**
```bash
# Single execution (development testing)
python main.py --once

# Continuous monitoring (production mode)
python main.py
```

### **4. Customer Onboarding**
```bash
# Start onboarding service
python core/tenant_onboarding.py

# Send customers personalized links:
# https://yourdomain.com/onboard/customer-name
```

---

## 📋 Core Components Explained

### **Pipeline Orchestration**
- **`main.py`**: Main entry point that coordinates the entire pipeline
- **`core/unified_file_analyzer.py`**: Orchestrates file processing, AI analysis, and routing

### **Security & Configuration**
- **`core/config.py`**: Environment-based configuration management (dev/prod)
- **`core/key_vault_service.py`**: Secure credential storage and retrieval
- **`core/tenant_loader.py`**: Multi-tenant credential and configuration loading

### **Email & Authentication**
- **`core/mail_reader.py`**: Microsoft Graph API integration for email processing
- **`core/tenant_onboarding.py`**: OAuth consent flow for customer self-service setup

### **File Processing**
- **`core/file_processors/factory.py`**: Determines appropriate processor for each file type
- **`core/file_processors/*.py`**: Specialized processors for different file formats
- **`core/interpreter/chatgpt_api.py`**: AI-powered document analysis and classification

### **Document Management**
- **`core/router.py`**: Intelligent folder routing based on document type and rules
- **`core/upload_onedrive.py`**: OneDrive integration for file organization
- **`core/notification.py`**: Email notifications for important documents

### **Utilities & Migration**
- **`setup_development.py`**: Automated development environment setup
- **`migrate_to_keyvault.py`**: Migration tool for existing JSON-based credentials
- **`test_multi_format.py`**: Testing utility for file processing capabilities

---

## 🔧 Configuration

### **Environment Variables** (`.env` file)
```bash
# Environment
MAIL_AUTO_ENVIRONMENT=development

# Development Environment Settings (for personal @outlook.com account)
DEV_KEY_VAULT_URL=https://your-dev-keyvault.vault.azure.net/
DEV_TENANT_ID=your-azure-tenant-id
DEV_CLIENT_ID=your-dev-app-client-id
DEV_CLIENT_SECRET=your-dev-app-client-secret
DEV_REDIRECT_URI=http://localhost....
DEV_USE_KEY_VAULT=true
DEV_USE_MANAGED_IDENTITY=false

# Production Environment Settings (for customer accounts)
PROD_KEY_VAULT_URL=https://your-prod-keyvault.vault.azure.net/
PROD_TENANT_ID=your-azure-tenant-id
PROD_CLIENT_ID=your-prod-app-client-id
PROD_CLIENT_SECRET=your-prod-app-client-secret
PROD_REDIRECT_URI=https://yourdomain.com/auth/callback
PROD_USE_KEY_VAULT=true
PROD_USE_MANAGED_IDENTITY=true
# OpenAI
OPENAI_API_KEY=sk-proj-your-openai-api-key

# Optional
LOG_LEVEL=INFO
DEBUG_MODE=false
```

### **Tenant Configuration** (`tenants/{customer}/config.json`)

#### **Supported Languages**
The `preferred_language` field supports any language that ChatGPT can understand. Common examples:
- `"English"` (default)
- `"Swedish"` / `"Svenska"`
- `"German"` / `"Deutsch"`
- `"French"` / `"Français"`
- `"Spanish"` / `"Español"`
- `"Italian"` / `"Italiano"`
- `"Dutch"` / `"Nederlands"`
- `"Norwegian"` / `"Norsk"`
- `"Danish"` / `"Dansk"`
- `"Finnish"` / `"Suomi"`

#### **Subfolder Format Placeholders**
- `{doc_type}` - Document type (e.g., "invoice", "certificate_of_analysis")
- `{yyyy}` - Current year (4 digits)
- `{mm}` - Current month (2 digits)
- `{document_year}` - Year extracted from document (e.g., signing date)
- `{company_name}` - Company name extracted from document
- `{supplier}` - Alias for company_name (backward compatibility)

#### **Email Template Placeholders**
- `{doc_type}` - Document type (e.g., "invoice", "certificate_of_analysis")
- `{summary}` - AI-generated summary of the document content
- `{recipient_name}` - First name of the email recipient (extracted from recipient's name)

#### **Language-Aware Email System**
The system automatically localizes email subjects and templates based on the `preferred_language` setting:

**Supported Languages:**
- English, Swedish, German, French, Spanish, Italian, Dutch, Norwegian, Danish, Finnish

**Email Behavior:**
- **Individual Emails**: Each recipient receives a separate, personalized email
- **Localized Subjects**: Email subjects are translated (e.g., "Nytt invoice mottaget" in Swedish)
- **Localized Templates**: Default templates use appropriate greetings ("Hej" in Swedish, "Hallo" in German)
- **Dynamic Names**: Each email uses the recipient's first name (e.g., "Hej Ahmed," or "Hi Quality,")

**Template Priority:**
1. Custom `email_template` in document type configuration (if provided)
2. Custom `email_template` in defaults (if provided)
3. Localized fallback template based on `preferred_language`

#### **Email Filtering Options**
- `process_external_only` - When `true`, only processes emails from external senders (outside company domains)
- `company_domains` - List of internal company domains to filter out when `process_external_only` is enabled

#### **Complete Configuration Example**
```json
{
  "tenant_name": "acme-corp",
  "defaults": {
    "storage": {
      "subfolder_format": "{doc_type}/{document_year}/{company_name}"
    },
    "notification": {
      "recipients": [],
      "email_template": "Hi {recipient_name},\n\nA new {doc_type} has arrived and was auto-filed.\n\n{summary}\n\nPlease review the attachment."
    },
    "classifier": {
      "confidence_threshold": 0.7
    },
    "actions": {
      "upload": true,
      "notify": false
    },
    "preferred_language": "English",
    "email_filtering": {
      "process_external_only": true,
      "company_domains": ["acme-corp.com", "company.com"]
    }
  },
  "document_types": {
    "invoice": {
      "keywords": ["invoice", "faktura", "amount due"],
      "notification": {
        "recipients": [
          {
            "name": "Accounts Payable",
            "email": "<EMAIL>"
          }
        ],
        "email_template": "Hi {recipient_name},\n\n{summary}\n\nKindly verify and schedule payment."
      },
      "storage": {
        "subfolder_format": "Invoices/{document_year}/{company_name}"
      },
      "actions": {
        "upload": true,
        "notify": true
      },
      "preferred_language": "Swedish"
    },
    "certificate_of_analysis": {
      "keywords": [
        "certificate of analysis",
        "certifikat av analys",
        "analyscertifikat"
      ],
      "notification": {
        "recipients": [
          {
            "name": "Quality Manager",
            "email": "<EMAIL>"
          }
        ],
        "email_template": "Hi {recipient_name},\n\nA new Certificate of Analysis has been received:\n\n{summary}\n\nPlease review the detailed analysis results."
      },
      "storage": {
        "subfolder_format": "Certificates/{document_year}/{company_name}"
      },
      "actions": {
        "upload": true,
        "notify": true
      },
      "preferred_language": "English"
    },
    "safety_data_sheet": {
      "keywords": [
        "safety data sheet",
        "säkerhetsdatablad",
        "SDS",
        "MSDS"
      ],
      "notification": {
        "recipients": [
          {
            "name": "Safety Officer",
            "email": "<EMAIL>"
          }
        ]
      },
      "storage": {
        "subfolder_format": "SafetyDataSheets/{document_year}/{company_name}"
      },
      "actions": {
        "upload": true,
        "notify": true
      },
      "preferred_language": "German"
    }
  }
}
```

---

## 📚 Documentation

- **[AZURE_SETUP.md](AZURE_SETUP.md)** - Complete Azure AD setup, production deployment, and customer onboarding
- **[KEYVAULT_SETUP.md](KEYVAULT_SETUP.md)** - Detailed Key Vault configuration and security setup

---

## 🔒 Security Features

- **Azure Key Vault**: Secure credential storage with encryption at rest
- **Managed Identity**: Production deployment without stored secrets
- **Tenant Isolation**: Complete separation of customer data and configurations
- **OAuth Consent**: Customer-controlled permission granting
- **Audit Logging**: Comprehensive tracking of all operations
- **Environment Separation**: Isolated development and production configurations

---

## 💰 Cost Structure

- **Azure Key Vault**: ~$0.018 per customer per month
- **Microsoft Graph API**: Free (with throttling limits)
- **Azure App Service**: $73-146/month (scales to thousands of customers)
- **Total**: ~$0.092 per customer per month at 1,000+ customers

---

## 🎯 Production Deployment

1. **Azure App Service**: Deploy with Managed Identity enabled
2. **Custom Domain**: Configure HTTPS for production OAuth callbacks  
3. **Customer Onboarding**: Send personalized links for self-service setup
4. **Monitoring**: Set up cost alerts and performance monitoring
5. **Scaling**: Horizontal scaling for 5,000+ customers

See [AZURE_SETUP.md](AZURE_SETUP.md) for detailed production deployment instructions.
