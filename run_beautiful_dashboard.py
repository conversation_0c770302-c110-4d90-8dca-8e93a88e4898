#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the beautiful bolt.new React dashboard with Mail_Auto backend integration.

This script will:
1. Check if Node.js is installed
2. Install dependencies if needed
3. Build the React app
4. Start the Flask backend serving the beautiful UI

If Node.js is not available, it falls back to the simple HTML dashboard.
"""

import os
import subprocess
import sys
import time
from pathlib import Path

def check_node_installed():
    """Check if Node.js and npm are installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js found: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js not found")
    return False

def check_npm_installed():
    """Check if npm is installed"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm found: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ npm not found")
    return False

def install_dependencies():
    """Install npm dependencies"""
    web_portal_path = Path("core/web_portal")
    
    if not web_portal_path.exists():
        print("❌ Web portal directory not found")
        return False
    
    print("📦 Installing npm dependencies...")
    try:
        result = subprocess.run(['npm', 'install'], cwd=web_portal_path, check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def build_react_app():
    """Build the React application"""
    web_portal_path = Path("core/web_portal")
    
    print("🔨 Building React application...")
    try:
        result = subprocess.run(['npm', 'run', 'build'], cwd=web_portal_path, check=True)
        print("✅ React app built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build React app: {e}")
        return False

def start_flask_server():
    """Start the Flask backend server"""
    print("🚀 Starting Flask backend server...")
    
    # Set environment variables for development
    os.environ['FLASK_ENV'] = 'development'
    os.environ['NODE_ENV'] = 'development'
    
    try:
        # Import and run the Flask app
        from web_server import app
        print("📊 Beautiful Dashboard: http://localhost:5000")
        print("📡 API Health: http://localhost:5000/api/health")
        print("💡 Press Ctrl+C to stop the server")
        
        app.run(host='0.0.0.0', port=5000, debug=True)
    except ImportError as e:
        print(f"❌ Failed to import Flask app: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return True

def main():
    """Main function to orchestrate the setup and launch"""
    print("🎨 Mail_Auto Beautiful Dashboard Setup")
    print("=" * 50)
    
    # Check if the built React app already exists
    dist_path = Path("core/web_portal/dist")
    if dist_path.exists():
        print("✅ React app already built, starting server...")
        start_flask_server()
        return
    
    # Check Node.js and npm
    if not check_node_installed() or not check_npm_installed():
        print("\n⚠️  Node.js/npm not found!")
        print("📝 Options:")
        print("   1. Install Node.js from: https://nodejs.org/")
        print("   2. Use simple dashboard: python simple_dev_server.py")
        print("   3. Continue anyway (will use fallback dashboard)")
        
        choice = input("\nContinue with fallback dashboard? (y/n): ").lower().strip()
        if choice == 'y':
            print("🔄 Starting with simple fallback dashboard...")
            start_flask_server()
        else:
            print("👋 Please install Node.js and try again")
        return
    
    # Check if node_modules exists
    node_modules_path = Path("core/web_portal/node_modules")
    if not node_modules_path.exists():
        if not install_dependencies():
            print("❌ Failed to install dependencies, using fallback dashboard")
            start_flask_server()
            return
    
    # Build the React app
    if not build_react_app():
        print("❌ Failed to build React app, using fallback dashboard")
        start_flask_server()
        return
    
    # Start the Flask server
    start_flask_server()

if __name__ == '__main__':
    main()
