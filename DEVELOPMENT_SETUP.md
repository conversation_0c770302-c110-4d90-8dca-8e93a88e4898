# Mail_Auto Web Portal Development Setup

This guide helps you set up the integrated development environment for the Mail_Auto web portal.

## 🏗️ Architecture Overview

```
Development Environment:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Mail_Auto     │
│   Vite:5173     │◄──►│   Flask:5000    │◄──►│   Core System   │
│   React/TS      │    │   API Server    │    │   Processing    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install frontend dependencies
cd core/web_portal
npm install
cd ../..

# Install Python dependencies (if not already done)
pip install -r requirements.txt
```

### 2. Start Development Environment

```bash
# Start both frontend and backend servers
python start_dev_server.py
```

This will start:
- **Flask Backend**: http://localhost:5000 (API endpoints)
- **Vite Frontend**: http://localhost:5173 (Web interface)

### 3. Access the Application

- **Main Application**: http://localhost:5173
- **API Health Check**: http://localhost:5000/api/health
- **Backend Direct**: http://localhost:5000

## 🔧 Development Features

### Hot Reload
- **Frontend**: Automatic reload on file changes (Vite)
- **Backend**: Manual restart required for Python changes

### API Proxy
- Frontend automatically proxies `/api/*` calls to Flask backend
- No CORS issues in development
- Real-time data from your Mail_Auto system

### Environment Indicator
- Development indicator in top-right corner
- Shows connection status to backend
- Quick access to API endpoints
- Processing status monitoring

## 📁 Project Structure

```
Mail_Auto/
├── core/
│   ├── web_portal/              # Frontend (React/TypeScript)
│   │   ├── src/
│   │   │   ├── components/      # React components
│   │   │   ├── pages/          # Page components
│   │   │   ├── services/       # API integration
│   │   │   └── types/          # TypeScript types
│   │   ├── package.json
│   │   └── vite.config.ts      # Vite configuration
│   ├── tenant_loader.py        # Your existing backend
│   ├── tracking/               # Your existing tracking
│   └── ...                     # Other Mail_Auto modules
├── web_server.py               # Flask API server
├── start_dev_server.py         # Development startup script
└── main.py                     # Your existing processor
```

## 🔌 API Integration

### Available Endpoints

```typescript
// Health check
GET /api/health

// Dashboard data
GET /api/dashboard/stats
GET /api/dashboard/recent-activity

// Admin functions
GET /api/admin/tenants
GET /api/admin/processing/status
POST /api/admin/processing/start
POST /api/admin/processing/stop
```

### Using the API Service

```typescript
import { dashboardApi, adminApi } from './services/api';

// Get dashboard statistics
const stats = await dashboardApi.getStats();

// Get tenant list
const tenants = await adminApi.getTenants();

// Start/stop processing
await adminApi.startProcessing();
```

## 🛠️ Development Workflow

### 1. Frontend Development
```bash
# Frontend changes auto-reload
cd core/web_portal
npm run dev  # Or use start_dev_server.py
```

### 2. Backend Development
```bash
# Restart backend after changes
python web_server.py
```

### 3. Full System Testing
```bash
# Test with real Mail_Auto processing
python main.py --once  # Process emails once
```

## 🔍 Debugging

### Frontend Debugging
- Open browser DevTools (F12)
- Check Console for API calls and errors
- Network tab shows API requests/responses

### Backend Debugging
- Flask logs appear in terminal
- API requests logged in development mode
- Use `/api/health` to test backend connectivity

### Integration Debugging
- Development indicator shows connection status
- Console logs all API calls in development
- Check both frontend (5173) and backend (5000) logs

## 📊 Live Data Integration

The web portal connects directly to your Mail_Auto system:

- **Real-time statistics** from your tracking database
- **Live tenant data** from your configuration files
- **Processing status** from your background processor
- **Document history** from your analytics system

## 🚀 Production Deployment

When ready for production:

```bash
# Build frontend
cd core/web_portal
npm run build

# Deploy to hosting service
# Frontend: Vercel, Netlify, or Azure Static Web Apps
# Backend: Azure App Service, Heroku, or VPS
```

## 🔧 Troubleshooting

### Frontend won't start
```bash
cd core/web_portal
rm -rf node_modules package-lock.json
npm install
```

### Backend API errors
- Check if Flask server is running on port 5000
- Verify CORS configuration in web_server.py
- Check Python dependencies are installed

### No data in dashboard
- Ensure your Mail_Auto system has processed some emails
- Check if tracking is enabled in tenant configurations
- Verify database connections in tracking system

## 💡 Tips

1. **Use the development indicator** to monitor system status
2. **Check browser console** for API errors and debugging info
3. **Test with your existing tenant data** for realistic development
4. **Use `python main.py --once`** to generate test data
5. **Keep both servers running** for best development experience

## 🎯 Next Steps

1. Customize the frontend components to match your needs
2. Add authentication and user management
3. Implement customer-specific data filtering
4. Add more admin tools and monitoring features
5. Prepare for production deployment
