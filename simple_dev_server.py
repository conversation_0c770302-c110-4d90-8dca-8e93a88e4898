#!/usr/bin/env python3
"""
Simplified development server for Mail_Auto web portal integration.
Serves a basic HTML interface that connects to your Mail_Auto backend.
No Node.js/npm required - pure Python solution.
"""

from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import os
import json
import threading
import time
from datetime import datetime, timedelta

# Import your existing Mail_Auto components
from core.tenant_loader import list_tenants
from core.tracking import get_tracking_service, DocumentAnalytics
from core.tracking.models import ProcessingStatus
from core.mailbox_manager import MailboxConfigManager

app = Flask(__name__)
CORS(app)

# Global variables for background processing
processing_thread = None
processing_active = False

def background_email_processor():
    """Background thread that runs the email processing loop"""
    global processing_active
    from main import process_tenants
    
    while processing_active:
        try:
            print(f"🔄 Background processing cycle - {datetime.now()}")
            process_tenants()
            time.sleep(300)  # 5 minutes between cycles
        except Exception as e:
            print(f"❌ Background processing error: {e}")
            time.sleep(60)  # Wait 1 minute on error

# ============================================================================
# SIMPLE HTML DASHBOARD (No React/npm required)
# ============================================================================

DASHBOARD_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mail_Auto Development Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; }
        .status-green { background-color: #10b981; }
        .status-red { background-color: #ef4444; }
        .status-yellow { background-color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Development Environment Indicator -->
    <div class="fixed top-4 right-4 bg-gray-900 text-white px-3 py-2 rounded-lg shadow-lg z-50">
        <div class="flex items-center gap-2">
            <div id="connection-status" class="status-indicator status-red"></div>
            <span class="text-sm font-medium">DEV MODE</span>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Mail_Auto Development Dashboard</h1>
        
        <!-- System Status -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">System Status</h3>
                <div id="system-status" class="text-2xl font-bold text-green-600">Loading...</div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Active Tenants</h3>
                <div id="tenant-count" class="text-2xl font-bold text-blue-600">-</div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Documents Processed</h3>
                <div id="document-count" class="text-2xl font-bold text-purple-600">-</div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Success Rate</h3>
                <div id="success-rate" class="text-2xl font-bold text-green-600">-</div>
            </div>
        </div>

        <!-- Processing Controls -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Processing Controls</h3>
            <div class="flex gap-4">
                <button id="start-processing" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Start Processing
                </button>
                <button id="stop-processing" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                    Stop Processing
                </button>
                <button id="refresh-data" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Refresh Data
                </button>
            </div>
            <div id="processing-status" class="mt-4 text-sm text-gray-600"></div>
        </div>

        <!-- Tenants List -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Tenants</h3>
            <div id="tenants-list" class="space-y-2">
                Loading tenants...
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div id="recent-activity" class="space-y-2">
                Loading activity...
            </div>
        </div>
    </div>

    <script>
        // API functions
        const api = {
            health: () => axios.get('/api/health'),
            stats: () => axios.get('/api/dashboard/stats'),
            tenants: () => axios.get('/api/admin/tenants'),
            activity: () => axios.get('/api/dashboard/recent-activity'),
            startProcessing: () => axios.post('/api/admin/processing/start'),
            stopProcessing: () => axios.post('/api/admin/processing/stop'),
            processingStatus: () => axios.get('/api/admin/processing/status')
        };

        // Update connection status
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('connection-status');
            if (connected) {
                indicator.className = 'status-indicator status-green';
            } else {
                indicator.className = 'status-indicator status-red';
            }
        }

        // Load dashboard data
        async function loadDashboard() {
            try {
                // Health check
                const health = await api.health();
                updateConnectionStatus(true);
                document.getElementById('system-status').textContent = 'Online';
                document.getElementById('tenant-count').textContent = health.data.tenants_count;

                // Dashboard stats
                const stats = await api.stats();
                document.getElementById('document-count').textContent = stats.data.total_documents;
                document.getElementById('success-rate').textContent = stats.data.success_rate.toFixed(1) + '%';

                // Tenants
                const tenants = await api.tenants();
                const tenantsList = document.getElementById('tenants-list');
                tenantsList.innerHTML = tenants.data.map(tenant => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div>
                            <span class="font-medium">${tenant.name}</span>
                            <span class="text-sm text-gray-500 ml-2">(${tenant.mailbox_count} mailboxes)</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            ${tenant.recent_documents} docs (7 days)
                        </div>
                    </div>
                `).join('');

                // Recent activity
                const activity = await api.activity();
                const activityList = document.getElementById('recent-activity');
                activityList.innerHTML = activity.data.slice(0, 10).map(item => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div>
                            <span class="font-medium">${item.filename}</span>
                            <span class="text-sm text-gray-500 ml-2">${item.tenant}</span>
                        </div>
                        <div class="text-sm ${item.status === 'success' ? 'text-green-600' : 'text-red-600'}">
                            ${item.status}
                        </div>
                    </div>
                `).join('');

                // Processing status
                const procStatus = await api.processingStatus();
                document.getElementById('processing-status').textContent = 
                    `Processing: ${procStatus.data.active ? 'Active' : 'Stopped'}`;

            } catch (error) {
                console.error('Dashboard load error:', error);
                updateConnectionStatus(false);
                document.getElementById('system-status').textContent = 'Offline';
            }
        }

        // Event listeners
        document.getElementById('start-processing').addEventListener('click', async () => {
            try {
                await api.startProcessing();
                alert('Processing started');
                loadDashboard();
            } catch (error) {
                alert('Error starting processing: ' + error.message);
            }
        });

        document.getElementById('stop-processing').addEventListener('click', async () => {
            try {
                await api.stopProcessing();
                alert('Processing stopped');
                loadDashboard();
            } catch (error) {
                alert('Error stopping processing: ' + error.message);
            }
        });

        document.getElementById('refresh-data').addEventListener('click', loadDashboard);

        // Initial load and auto-refresh
        loadDashboard();
        setInterval(loadDashboard, 30000); // Refresh every 30 seconds
    </script>
</body>
</html>
"""

# ============================================================================
# API ENDPOINTS
# ============================================================================

@app.route('/')
def dashboard():
    """Serve the simple HTML dashboard"""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "processing_active": processing_active,
        "tenants_count": len(list_tenants())
    })

@app.route('/api/dashboard/stats')
def dashboard_stats():
    """Get dashboard statistics"""
    tenants = list_tenants()
    tracking_service = get_tracking_service()
    
    if not tracking_service.is_enabled():
        return jsonify({
            "total_documents": 0,
            "successful_documents": 0,
            "failed_documents": 0,
            "success_rate": 0
        })
    
    # Aggregate stats from all tenants
    total_docs = 0
    successful_docs = 0
    
    for tenant_name, _, _, _ in tenants:
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
            analytics = DocumentAnalytics(tracking_service.database)
            stats = analytics.get_period_statistics(tenant_name, start_date, end_date)
            total_docs += stats.total_documents
            successful_docs += stats.successful_documents
        except:
            continue
    
    return jsonify({
        "total_documents": total_docs,
        "successful_documents": successful_docs,
        "failed_documents": total_docs - successful_docs,
        "success_rate": (successful_docs / total_docs * 100) if total_docs > 0 else 0
    })

@app.route('/api/admin/tenants')
def admin_tenants():
    """Get list of all tenants"""
    tenants = list_tenants()
    tenant_list = []
    
    for tenant_name, creds_source, config, _ in tenants:
        mailbox_manager = MailboxConfigManager(config)
        mailboxes = []
        
        if mailbox_manager.has_multi_mailbox_config():
            for mailbox in mailbox_manager.get_enabled_mailboxes():
                mailboxes.append({
                    "email": mailbox.email,
                    "display_name": mailbox.display_name,
                    "enabled": mailbox.enabled
                })
        
        tenant_list.append({
            "name": tenant_name,
            "storage_type": "key_vault" if creds_source == "key_vault" else "file",
            "mailboxes": mailboxes,
            "mailbox_count": len(mailboxes),
            "recent_documents": 0  # Simplified for now
        })
    
    return jsonify(tenant_list)

@app.route('/api/dashboard/recent-activity')
def recent_activity():
    """Get recent document processing activity"""
    tracking_service = get_tracking_service()
    if not tracking_service.is_enabled():
        return jsonify([])
    
    try:
        recent_events = []
        tenants = list_tenants()
        
        for tenant_name, _, _, _ in tenants:
            events = tracking_service.database.get_recent_events(tenant_name, limit=5)
            for event in events:
                recent_events.append({
                    "filename": event.filename,
                    "tenant": tenant_name,
                    "status": event.status.value,
                    "processing_date": event.processing_date.isoformat()
                })
        
        recent_events.sort(key=lambda x: x["processing_date"], reverse=True)
        return jsonify(recent_events[:20])
    except:
        return jsonify([])

@app.route('/api/admin/processing/start', methods=['POST'])
def start_processing():
    """Start background processing"""
    global processing_thread, processing_active
    
    if processing_active:
        return jsonify({"error": "Processing already active"}), 400
    
    processing_active = True
    processing_thread = threading.Thread(target=background_email_processor, daemon=True)
    processing_thread.start()
    
    return jsonify({"message": "Background processing started"})

@app.route('/api/admin/processing/stop', methods=['POST'])
def stop_processing():
    """Stop background processing"""
    global processing_active
    processing_active = False
    return jsonify({"message": "Background processing stopped"})

@app.route('/api/admin/processing/status')
def processing_status():
    """Get processing status"""
    return jsonify({
        "active": processing_active,
        "thread_alive": processing_thread.is_alive() if processing_thread else False
    })

if __name__ == '__main__':
    print("🚀 Mail_Auto Simple Development Dashboard")
    print("📊 Dashboard: http://localhost:5000")
    print("📡 API Health: http://localhost:5000/api/health")
    print("💡 No Node.js required - pure Python solution!")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
