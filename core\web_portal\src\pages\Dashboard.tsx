import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { FileText, Mail, CheckCircle, AlertCircle, TrendingUp, Activity, RefreshCw, Clock } from 'lucide-react';
import Layout from '../components/common/Layout';
import Card from '../components/common/Card';
// import ConnectionStatus from '../components/common/ConnectionStatus';

import { dashboardApi, healthApi, type DashboardStats, type LiveStats, type RecentActivity, type DocumentType, type DailyActivity, type MonthlyTrend } from '../services/api';
import { useApp } from '../contexts/AppContext';

const Dashboard: React.FC = () => {
  const { isDevelopmentMode, toggleDevelopmentMode } = useApp();

  // State for live data
  const [liveStats, setLiveStats] = useState<LiveStats | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [dailyActivity, setDailyActivity] = useState<DailyActivity[]>([]);
  const [monthlyTrend, setMonthlyTrend] = useState<MonthlyTrend[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  // Fetch live data from backend
  const fetchLiveData = async () => {
    try {
      setIsLoading(true);

      // Fetch all data in parallel
      const [liveStatsRes, dashboardStatsRes, recentActivityRes, documentTypesRes, dailyActivityRes, monthlyTrendRes, healthRes] = await Promise.all([
        dashboardApi.getLiveStats(isDevelopmentMode),
        dashboardApi.getStats(isDevelopmentMode),
        dashboardApi.getRecentActivity(isDevelopmentMode),
        dashboardApi.getDocumentTypes(isDevelopmentMode),
        dashboardApi.getDailyActivity(isDevelopmentMode),
        dashboardApi.getMonthlyTrend(isDevelopmentMode),
        healthApi.check()
      ]);

      setLiveStats(liveStatsRes);
      setDashboardStats(dashboardStatsRes);
      setRecentActivity(recentActivityRes);
      setDocumentTypes(documentTypesRes);
      setDailyActivity(dailyActivityRes);
      setMonthlyTrend(monthlyTrendRes);
      setIsConnected(true);
      setLastUpdated(new Date());

    } catch (error) {
      console.error('❌ Failed to fetch live data:', error);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh data every 30 seconds and when development mode changes
  useEffect(() => {
    fetchLiveData();

    const interval = setInterval(fetchLiveData, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isDevelopmentMode]); // Re-fetch when development mode changes

  // Use only live dashboard stats - no fallback to mock data
  const stats = dashboardStats;

  const quickStats = [
    {
      title: 'Documents Today',
      value: liveStats ? liveStats.total_documents_today.toLocaleString() : '0',
      icon: <FileText className="h-8 w-8 text-blue-600" />,
      change: liveStats ? `${liveStats.processing_rate_per_hour.toFixed(1)}/hr` : 'No data',
      changeType: 'neutral' as const
    },
    {
      title: 'Success Rate Today',
      value: liveStats ? `${liveStats.success_rate_today.toFixed(1)}%` : '0%',
      icon: <CheckCircle className="h-8 w-8 text-green-600" />,
      change: liveStats ? `${liveStats.successful_today}/${liveStats.total_documents_today}` : 'No data',
      changeType: 'neutral' as const
    },
    {
      title: 'Active Tenants',
      value: liveStats ? liveStats.active_tenants.toString() : '0',
      icon: <Mail className="h-8 w-8 text-purple-600" />,
      change: isConnected ? 'Live' : 'Offline',
      changeType: isConnected ? 'positive' : 'negative'
    },
    {
      title: 'Last Processed',
      value: liveStats?.last_processed ?
        new Date(liveStats.last_processed).toLocaleTimeString() :
        'No recent activity',
      icon: <Clock className="h-8 w-8 text-orange-600" />,
      change: liveStats?.last_processed ?
        `${Math.round((Date.now() - new Date(liveStats.last_processed).getTime()) / 60000)}m ago` :
        'N/A',
      changeType: 'neutral'
    }
  ];

  // Use only live document types data - no fallback to mock data
  const documentTypeData = documentTypes;

  // Use only live recent activity data - no fallback to mock data
  const recentDocuments = recentActivity.length > 0 ?
    recentActivity.slice(0, 5).map(activity => ({
      id: activity.id,
      filename: activity.filename,
      type: activity.document_type,
      status: activity.status === 'success' ? 'completed' :
        activity.status === 'processing' ? 'processing' : 'failed',
      processedAt: activity.processing_date,
      tenant: activity.tenant,
      mailbox: activity.mailbox,
      fileSize: activity.file_size,
      processingTime: activity.processing_time_ms
    })) : [];

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
            <p className="text-gray-600 dark:text-gray-400">Monitor your document processing activity</p>
          </div>

          <div className="flex items-center gap-4">
            {/* Development/Production Mode Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Mode:</span>
              <button
                onClick={toggleDevelopmentMode}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${isDevelopmentMode
                  ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                  : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  }`}
              >
                {isDevelopmentMode ? '🔧 Development' : '🏢 Production'}
              </button>
            </div>

            {/* Connection Status */}
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>

            {/* Last Updated */}
            {lastUpdated && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Updated {lastUpdated.toLocaleTimeString()}
              </span>
            )}

            {/* Refresh Button */}
            <button
              onClick={fetchLiveData}
              disabled={isLoading}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                  <p className={`text-sm ${stat.changeType === 'positive' ? 'text-green-600' :
                    stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-500'
                    }`}>
                    {stat.change} from last month
                  </p>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  {stat.icon}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Daily Processing Chart */}
          <Card title="Daily Processing Activity" description="Last 7 days document processing">
            {dailyActivity.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={dailyActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                    formatter={(value, name) => [value, name === 'successful' ? 'Successful' : name === 'failed' ? 'Failed' : 'Documents']}
                  />
                  <Bar dataKey="successful" stackId="a" fill="#10B981" />
                  <Bar dataKey="failed" stackId="a" fill="#EF4444" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px]">
                <div className="text-center">
                  <FileText className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 font-medium">No daily processing data</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                    {isDevelopmentMode
                      ? 'Process some documents to see daily activity'
                      : 'No processing activity data available'}
                  </p>
                </div>
              </div>
            )}
          </Card>

          {/* Monthly Trend */}
          <Card title="Monthly Trend" description="Document processing over time">
            {monthlyTrend.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={monthlyTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month_name" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="documents" stroke="#3B82F6" strokeWidth={2} />
                  <Line type="monotone" dataKey="successful" stroke="#10B981" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px]">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 font-medium">No monthly trend data</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                    {isDevelopmentMode
                      ? 'Process documents over time to see trends'
                      : 'No monthly processing data available'}
                  </p>
                </div>
              </div>
            )}
          </Card>
        </div>

        {/* Document Types and Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Document Types Distribution */}
          <Card
            title="Document Types"
            description={`Distribution by document type ${isDevelopmentMode ? '(Development)' : '(Production)'} ${isConnected ? '(Live Data)' : '(Connecting...)'}`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center h-[300px]">
                <div className="flex flex-col items-center space-y-3">
                  <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                  <p className="text-gray-500 dark:text-gray-400">Loading document types...</p>
                </div>
              </div>
            ) : documentTypeData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={documentTypeData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name && name.length > 15 ? name.substring(0, 15) + '...' : name || ''} (${((percent || 0) * 100).toFixed(0)}%)`}
                    labelLine={false}
                  >
                    {documentTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [value, name]} />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px]">
                <div className="flex flex-col items-center space-y-3 text-center">
                  <FileText className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400 font-medium">No document types found</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                      {isDevelopmentMode
                        ? 'Process some test documents to see data here'
                        : 'No documents have been processed yet'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Recent Documents */}
          <Card title="Recent Documents" description={`Latest processed documents ${isConnected ? '(Live Data)' : '(Connecting...)'}`}>
            <div className="space-y-4">
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg animate-pulse">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                        <div>
                          <div className="w-32 h-4 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                          <div className="w-24 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="w-16 h-3 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                        <div className="w-12 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentDocuments.length > 0 ? recentDocuments.map((doc) => (
                <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${doc.status === 'completed' ? 'bg-green-100 text-green-600' :
                      doc.status === 'processing' ? 'bg-yellow-100 text-yellow-600' :
                        'bg-red-100 text-red-600'
                      }`}>
                      {doc.status === 'completed' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : doc.status === 'processing' ? (
                        <Activity className="h-4 w-4" />
                      ) : (
                        <AlertCircle className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{doc.filename}</p>
                      <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                        <span className="capitalize">{doc.type}</span>
                        {doc.tenant && (
                          <>
                            <span>•</span>
                            <span>{doc.tenant}</span>
                          </>
                        )}
                        {doc.fileSize && (
                          <>
                            <span>•</span>
                            <span>{(doc.fileSize / 1024).toFixed(1)} KB</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-medium capitalize ${doc.status === 'completed' ? 'text-green-600' :
                      doc.status === 'processing' ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                      {doc.status}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(doc.processedAt).toLocaleString()}
                    </p>
                    {doc.processingTime && (
                      <p className="text-xs text-gray-400">
                        {doc.processingTime}ms
                      </p>
                    )}
                  </div>
                </div>
              )) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400 font-medium">No recent documents</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                      {isDevelopmentMode
                        ? 'Process some test documents to see activity here'
                        : 'No documents have been processed recently'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;