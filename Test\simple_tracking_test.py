"""Simple test to verify tracking system works."""

import os
import sys
import tempfile
from datetime import datetime

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.tracking import (
    ProcessingEvent, ProcessingStatus, OperationType,
    DocumentTrackingService, initialize_tracking
)

def test_basic_tracking():
    """Test basic tracking functionality."""
    print("🧪 Testing basic tracking functionality...")
    
    # Create a temporary database
    temp_db_path = os.path.join(tempfile.gettempdir(), "simple_test.db")
    
    try:
        # Initialize tracking
        initialize_tracking(temp_db_path, enabled=True)
        
        # Create tracking service
        service = DocumentTrackingService(temp_db_path, enabled=True)
        
        # Test context manager
        print("📝 Testing context manager...")
        with service.track_processing(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            filename="test_document.pdf",
            file_size=1024
        ) as tracker:
            tracker.set_document_type("invoice")
            tracker.set_upload_folder("Invoices/2024/TestCompany")
            tracker.set_notification_recipients(["<EMAIL>"])
            print("✅ Context manager completed successfully")
        
        # Test simple event logging
        print("📝 Testing simple event logging...")
        result = service.log_simple_event(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            document_type="certificate",
            filename="certificate.pdf",
            file_size=2048,
            status=ProcessingStatus.SUCCESS,
            operation_type=OperationType.UPLOAD
        )
        
        if result:
            print("✅ Simple event logged successfully")
        else:
            print("❌ Failed to log simple event")
            return False
        
        # Test retrieving events
        print("📝 Testing event retrieval...")
        recent_events = service.get_recent_events("test_tenant", 10)
        print(f"📊 Retrieved {len(recent_events)} recent events")
        
        for event in recent_events:
            print(f"  - {event.filename} ({event.document_type}) - {event.status.value}")
        
        # Test statistics
        print("📝 Testing statistics...")
        from datetime import date
        today = date.today()
        daily_stats = service.get_daily_stats("test_tenant", today)
        
        if daily_stats:
            print(f"📊 Daily stats: {daily_stats.total_documents} documents, {daily_stats.successful_documents} successful")
        else:
            print("📊 No daily stats found")
        
        print("🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)
            print("🧹 Cleaned up test database")

if __name__ == "__main__":
    success = test_basic_tracking()
    sys.exit(0 if success else 1)
