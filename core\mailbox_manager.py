"""
Multi-mailbox configuration manager for tenant email automation.
Provides utilities for managing mailbox configurations that will be web-application ready.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class MailboxConfig:
    """Configuration for a single mailbox."""
    email: str
    enabled: bool
    display_name: str
    document_types: Dict[str, Any] = None

    def __post_init__(self):
        if self.document_types is None:
            self.document_types = {}


@dataclass
class ProcessingContext:
    """Context information for document processing."""
    mailbox_email: str
    tenant_name: str
    document_type: str
    filename: str


class MailboxConfigManager:
    """
    Manager for multi-mailbox tenant configurations.
    Handles configuration merging, validation, and mailbox-specific settings.
    """

    def __init__(self, tenant_config: Dict[str, Any]):
        """
        Initialize with tenant configuration.
        
        Args:
            tenant_config: Complete tenant configuration dictionary
        """
        self.config = tenant_config
        self.defaults = tenant_config.get("defaults", {})
        self.global_document_types = tenant_config.get("document_types", {})
        self.mailboxes_config = tenant_config.get("mailboxes", {})

    def has_mailbox_configuration(self) -> bool:
        """Check if this tenant has multi-mailbox configuration."""
        return "mailboxes" in self.config and bool(self.mailboxes_config)

    def is_document_type_enabled_for_mailbox(self, mailbox_email: str, doc_type: str) -> bool:
        """
        Check if a document type is enabled for processing in a specific mailbox.

        Args:
            mailbox_email: Email address of the mailbox
            doc_type: Document type to check

        Returns:
            True if the document type should be processed for this mailbox
        """
        if not self.has_mailbox_configuration():
            return False

        # Get mailbox configuration
        mailbox_config = self.mailboxes_config.get(mailbox_email, {})

        # Check if mailbox is enabled
        if not mailbox_config.get("enabled", True):
            return False

        # Check if document type is globally enabled
        if doc_type in self.global_document_types:
            return self.global_document_types[doc_type].get("enabled", True)

        # Default to enabled if not explicitly configured
        return True

    def get_merged_document_config(self, mailbox_email: str, doc_type: str) -> Dict[str, Any]:
        """
        Get merged configuration for a document type.
        Merges defaults -> global document config (no mailbox-specific overrides in new structure).

        Args:
            mailbox_email: Email address of the mailbox (for compatibility)
            doc_type: Document type

        Returns:
            Merged configuration dictionary
        """
        # Start with defaults
        merged_config = {}

        # Apply defaults
        default_actions = self.defaults.get("actions", {})
        default_notification = self.defaults.get("notification", {})
        default_storage = self.defaults.get("storage", {})

        if default_actions:
            merged_config["actions"] = default_actions.copy()
        if default_notification:
            merged_config["notification"] = default_notification.copy()
        if default_storage:
            merged_config["storage"] = default_storage.copy()

        # Apply global document type configuration
        global_doc_config = self.global_document_types.get(doc_type, {})

        for key, value in global_doc_config.items():
            if key in ["actions", "notification", "storage"]:
                if key not in merged_config:
                    merged_config[key] = {}
                merged_config[key].update(value)
            else:
                merged_config[key] = value

        return merged_config

    def get_enabled_mailboxes(self) -> List[MailboxConfig]:
        """Get list of enabled mailboxes."""
        if not self.has_mailbox_configuration():
            return []

        enabled = []
        for email, config in self.config["mailboxes"].items():
            if config.get("enabled", True):  # Default to enabled
                enabled.append(MailboxConfig(
                    email=email,
                    display_name=config.get("display_name", email),
                    enabled=True,
                    document_types={}  # No longer used in new structure
                ))

        return enabled

    def should_notify_for_document(self, mailbox_email: str, doc_type: str) -> bool:
        """
        Check if notifications should be sent for a document type in a mailbox.

        Args:
            mailbox_email: Email address of the mailbox
            doc_type: Document type

        Returns:
            True if notifications should be sent
        """
        config = self.get_merged_document_config(mailbox_email, doc_type)
        return config.get("actions", {}).get("notify", False)

    def get_notification_recipients(self, mailbox_email: str, doc_type: str) -> List[Dict[str, str]]:
        """
        Get notification recipients for a document type in a mailbox.

        Args:
            mailbox_email: Email address of the mailbox
            doc_type: Document type

        Returns:
            List of recipient dictionaries with 'name' and 'email' keys
        """
        config = self.get_merged_document_config(mailbox_email, doc_type)
        return config.get("notification", {}).get("recipients", [])


def create_processing_context(
    mailbox_email: str,
    tenant_name: str,
    document_type: str,
    filename: str
) -> ProcessingContext:
    """
    Create a processing context for tracking document processing.
    
    Args:
        mailbox_email: Email address of the mailbox that received the document
        tenant_name: Name of the tenant
        document_type: Type of document being processed
        filename: Original filename of the document
        
    Returns:
        ProcessingContext object
    """
    return ProcessingContext(
        mailbox_email=mailbox_email,
        tenant_name=tenant_name,
        document_type=document_type,
        filename=filename
    )


# Web-application ready utility functions for future integration

def validate_mailbox_config(mailbox_config: Dict[str, Any]) -> List[str]:
    """
    Validate a mailbox configuration.
    
    Args:
        mailbox_config: Mailbox configuration dictionary
        
    Returns:
        List of validation error messages (empty if valid)
    """
    errors = []
    
    if not isinstance(mailbox_config, dict):
        errors.append("Mailbox configuration must be a dictionary")
        return errors
    
    # Check required fields
    if "email" not in mailbox_config:
        errors.append("Mailbox configuration must include 'email' field")
    
    # Validate email format (basic check)
    email = mailbox_config.get("email", "")
    if email and "@" not in email:
        errors.append(f"Invalid email format: {email}")
    
    # Validate enabled field
    enabled = mailbox_config.get("enabled")
    if enabled is not None and not isinstance(enabled, bool):
        errors.append("'enabled' field must be a boolean")
    
    # Validate document_types if present
    doc_types = mailbox_config.get("document_types")
    if doc_types is not None and not isinstance(doc_types, dict):
        errors.append("'document_types' field must be a dictionary")
    
    return errors


def add_mailbox_to_config(
    tenant_config: Dict[str, Any],
    mailbox_email: str,
    display_name: str = None,
    enabled: bool = True
) -> Dict[str, Any]:
    """
    Add a new mailbox to tenant configuration.

    Args:
        tenant_config: Existing tenant configuration
        mailbox_email: Email address of the mailbox to add
        display_name: Display name for the mailbox (optional)
        enabled: Whether the mailbox should be enabled

    Returns:
        Updated tenant configuration
    """
    # Ensure mailboxes section exists
    if "mailboxes" not in tenant_config:
        tenant_config["mailboxes"] = {}

    # Add the mailbox (simplified structure - no document_types per mailbox)
    tenant_config["mailboxes"][mailbox_email] = {
        "enabled": enabled,
        "display_name": display_name or mailbox_email
    }

    return tenant_config


def remove_mailbox_from_config(
    tenant_config: Dict[str, Any],
    mailbox_email: str
) -> Dict[str, Any]:
    """
    Remove a mailbox from tenant configuration.
    
    Args:
        tenant_config: Existing tenant configuration
        mailbox_email: Email address of the mailbox to remove
        
    Returns:
        Updated tenant configuration
    """
    if "mailboxes" in tenant_config and mailbox_email in tenant_config["mailboxes"]:
        del tenant_config["mailboxes"][mailbox_email]
    
    return tenant_config
