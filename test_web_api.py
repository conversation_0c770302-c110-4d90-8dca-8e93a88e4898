#!/usr/bin/env python3
"""Test script to verify web API endpoints work correctly."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from web_server import app
import json

def test_dashboard_stats():
    """Test the dashboard stats endpoint"""
    print("🧪 Testing dashboard stats endpoint...")
    
    with app.test_client() as client:
        response = client.get('/api/dashboard/stats')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            print("Dashboard stats:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"Error: {response.data}")
            return False

def test_live_stats():
    """Test the live stats endpoint"""
    print("\n🧪 Testing live stats endpoint...")
    
    with app.test_client() as client:
        response = client.get('/api/dashboard/live-stats?dev_mode=true')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            print("Live stats:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"Error: {response.data}")
            return False

def test_tenant_activity():
    """Test the tenant activity endpoint"""
    print("\n🧪 Testing tenant activity endpoint...")
    
    with app.test_client() as client:
        response = client.get('/api/admin/tenant-activity?dev_mode=true')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            print("Tenant activity:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"Error: {response.data}")
            return False

def test_recent_activity():
    """Test the recent activity endpoint"""
    print("\n🧪 Testing recent activity endpoint...")
    
    with app.test_client() as client:
        response = client.get('/api/dashboard/recent-activity?dev_mode=true')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            print("Recent activity:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"Error: {response.data}")
            return False

def test_production_mode_consistency():
    """Test that production mode returns consistent data structures"""
    print("\n🧪 Testing production mode consistency...")

    with app.test_client() as client:
        # Test dashboard stats in production mode
        response = client.get('/api/dashboard/stats?dev_mode=false')
        if response.status_code != 200:
            print(f"❌ Dashboard stats failed: {response.status_code}")
            return False

        dashboard_data = response.get_json()
        required_dashboard_fields = ['total_documents', 'successful_documents', 'failed_documents', 'success_rate', 'active_tenants', 'processing_enabled']

        # Test live stats in production mode
        response = client.get('/api/dashboard/live-stats?dev_mode=false')
        if response.status_code != 200:
            print(f"❌ Live stats failed: {response.status_code}")
            return False

        live_data = response.get_json()
        required_live_fields = ['total_documents_today', 'successful_today', 'failed_today', 'success_rate_today', 'processing_rate_per_hour', 'last_processed', 'active_tenants']

        # Check all required fields are present and numeric fields are numbers
        missing_dashboard = [f for f in required_dashboard_fields if f not in dashboard_data]
        missing_live = [f for f in required_live_fields if f not in live_data]

        if missing_dashboard:
            print(f"❌ Missing dashboard fields: {missing_dashboard}")
            return False

        if missing_live:
            print(f"❌ Missing live stats fields: {missing_live}")
            return False

        # Check that numeric fields are actually numbers (not None)
        numeric_dashboard_fields = ['total_documents', 'successful_documents', 'failed_documents', 'success_rate']
        numeric_live_fields = ['total_documents_today', 'successful_today', 'failed_today', 'success_rate_today', 'processing_rate_per_hour']

        for field in numeric_dashboard_fields:
            if not isinstance(dashboard_data[field], (int, float)):
                print(f"❌ Dashboard field '{field}' is not numeric: {dashboard_data[field]} (type: {type(dashboard_data[field])})")
                return False

        for field in numeric_live_fields:
            if not isinstance(live_data[field], (int, float)):
                print(f"❌ Live stats field '{field}' is not numeric: {live_data[field]} (type: {type(live_data[field])})")
                return False

        print("✅ Production mode data structures are consistent")
        return True

if __name__ == "__main__":
    print("🚀 Testing Mail_Auto Web API endpoints...")

    results = []
    results.append(test_dashboard_stats())
    results.append(test_live_stats())
    results.append(test_tenant_activity())
    results.append(test_recent_activity())
    results.append(test_production_mode_consistency())

    print(f"\n📊 Results: {sum(results)}/{len(results)} tests passed")

    if all(results):
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
